import { defineStore } from 'pinia'
import { ref } from 'vue'

/**
 * 标签页视图状态管理
 * 管理页面标签的打开、关闭、缓存等功能
 */
export const useTagsViewStore = defineStore('tagsView', () => {
  // 状态
  const visitedViews = ref([]) // 访问过的视图列表
  const cachedViews = ref([])  // 缓存的视图列表（用于keep-alive）

  // 存储键名
  const STORAGE_KEY = 'tags_view_visited'

  /**
   * 初始化标签页数据
   * 从localStorage恢复之前的标签页状态
   */
  const initTagsView = () => {
    try {
      const stored = localStorage.getItem(STORAGE_KEY)
      if (stored) {
        const parsedViews = JSON.parse(stored)
        if (Array.isArray(parsedViews)) {
          visitedViews.value = parsedViews
          // 恢复缓存列表，优先使用组件名称
          cachedViews.value = parsedViews
            .filter(view => view.meta?.keepAlive !== false)
            .map(view => view.meta?.componentName || view.name)
            .filter(Boolean)
            .filter((name, index, arr) => arr.indexOf(name) === index) // 去重
        }
      }
    } catch (error) {
      console.warn('恢复标签页数据失败:', error)
      visitedViews.value = []
      cachedViews.value = []
    }

    // 确保首页标签存在
    addDefaultDashboard()
  }

  /**
   * 添加默认的仪表盘标签
   */
  const addDefaultDashboard = () => {
    const dashboardView = {
      path: '/dashboard',
      name: 'Dashboard',
      title: '仪表盘',
      meta: {
        title: '仪表盘',
        icon: 'all-application',
        affix: true, // 固定标签，不可关闭
        keepAlive: true
      },
      query: {},
      params: {}
    }

    // 检查是否已存在仪表盘标签
    const exists = visitedViews.value.some(view => view.path === '/dashboard')
    if (!exists) {
      visitedViews.value.unshift(dashboardView)
      saveToStorage()
    }
  }

  /**
   * 保存标签页数据到localStorage
   */
  const saveToStorage = () => {
    try {
      localStorage.setItem(STORAGE_KEY, JSON.stringify(visitedViews.value))
    } catch (error) {
      console.warn('保存标签页数据失败:', error)
    }
  }

  /**
   * 添加访问的视图
   * @param {Object} view 视图对象
   */
  const addView = (view) => {
    addVisitedView(view)
    addCachedView(view)
  }

  /**
   * 添加访问过的视图
   * @param {Object} view 视图对象
   */
  const addVisitedView = (view) => {
    // 检查是否已存在相同路径的标签
    const existingIndex = visitedViews.value.findIndex(v => v.path === view.path)

    if (existingIndex !== -1) {
      // 如果存在，更新标签信息，但保持固定标签的特殊属性
      const existingView = visitedViews.value[existingIndex]
      visitedViews.value[existingIndex] = {
        ...existingView,
        ...view,
        // 对于仪表盘，保持其固定属性和图标
        meta: view.path === '/dashboard' ? {
          ...view.meta,
          icon: 'all-application',
          affix: true,
          keepAlive: true
        } : view.meta || {},
        // 保持查询参数和路由参数的更新
        query: view.query || {},
        params: view.params || {}
      }
    } else {
      // 如果不存在，添加新标签
      const newView = {
        path: view.path,
        name: view.name,
        title: view.meta?.title || view.title || '未命名页面',
        meta: view.path === '/dashboard' ? {
          ...view.meta,
          icon: 'all-application',
          affix: true,
          keepAlive: true
        } : view.meta || {},
        query: view.query || {},
        params: view.params || {}
      }

      visitedViews.value.push(newView)

      // 限制标签数量（可配置）
      const maxTabs = 20
      if (visitedViews.value.length > maxTabs) {
        // 移除最旧的非固定标签
        const removableIndex = visitedViews.value.findIndex(v => !v.meta?.affix)
        if (removableIndex !== -1) {
          visitedViews.value.splice(removableIndex, 1)
        }
      }
    }

    saveToStorage()
  }

  /**
   * 添加缓存视图
   * @param {Object} view 视图对象
   */
  const addCachedView = (view) => {
    // 检查是否需要缓存（默认缓存，除非明确设置为false）
    const shouldCache = view.meta?.keepAlive !== false
    if (!shouldCache) return

    // 优先使用组件名称，如果没有则使用路由名称
    const cacheKey = view.meta?.componentName || view.name
    if (!cacheKey) return

    if (!cachedViews.value.includes(cacheKey)) {
      cachedViews.value.push(cacheKey)
    }
  }

  /**
   * 删除视图
   * @param {Object} view 要删除的视图
   * @returns {Object} 删除结果
   */
  const delView = (view) => {
    return new Promise((resolve) => {
      delVisitedView(view)
      delCachedView(view)
      resolve({
        visitedViews: [...visitedViews.value],
        cachedViews: [...cachedViews.value]
      })
    })
  }

  /**
   * 删除访问过的视图
   * @param {Object} view 要删除的视图
   */
  const delVisitedView = (view) => {
    const index = visitedViews.value.findIndex(v => v.path === view.path)
    if (index !== -1) {
      // 检查是否为固定标签
      if (visitedViews.value[index].meta?.affix) {
        return // 固定标签不能删除
      }
      visitedViews.value.splice(index, 1)
      saveToStorage()
    }
  }

  /**
   * 删除缓存视图
   * @param {Object} view 要删除的视图
   */
  const delCachedView = (view) => {
    // 优先使用组件名称，如果没有则使用路由名称
    const cacheKey = view.meta?.componentName || view.name
    if (!cacheKey) return

    const index = cachedViews.value.indexOf(cacheKey)
    if (index !== -1) {
      cachedViews.value.splice(index, 1)
    }
  }

  /**
   * 删除其他视图
   * @param {Object} view 要保留的视图
   * @returns {Object} 删除结果
   */
  const delOthersViews = (view) => {
    return new Promise((resolve) => {
      delOthersVisitedViews(view)
      delOthersCachedViews(view)
      resolve({
        visitedViews: [...visitedViews.value],
        cachedViews: [...cachedViews.value]
      })
    })
  }

  /**
   * 删除其他访问过的视图
   * @param {Object} view 要保留的视图
   */
  const delOthersVisitedViews = (view) => {
    visitedViews.value = visitedViews.value.filter(v => {
      return v.meta?.affix || v.path === view.path
    })
    saveToStorage()
  }

  /**
   * 删除其他缓存视图
   * @param {Object} view 要保留的视图
   */
  const delOthersCachedViews = (view) => {
    // 优先使用组件名称，如果没有则使用路由名称
    const cacheKey = view.meta?.componentName || view.name
    if (!cacheKey) return

    cachedViews.value = cachedViews.value.filter(name => name === cacheKey)
  }

  /**
   * 删除所有视图
   * @returns {Object} 删除结果
   */
  const delAllViews = () => {
    return new Promise((resolve) => {
      delAllVisitedViews()
      delAllCachedViews()
      resolve({
        visitedViews: [...visitedViews.value],
        cachedViews: [...cachedViews.value]
      })
    })
  }

  /**
   * 删除所有访问过的视图
   */
  const delAllVisitedViews = () => {
    // 只保留固定标签
    visitedViews.value = visitedViews.value.filter(view => view.meta?.affix)
    saveToStorage()
  }

  /**
   * 删除所有缓存视图
   */
  const delAllCachedViews = () => {
    cachedViews.value = []
  }

  /**
   * 更新访问过的视图
   * @param {Object} view 要更新的视图
   */
  const updateVisitedView = (view) => {
    const index = visitedViews.value.findIndex(v => v.path === view.path)
    if (index !== -1) {
      visitedViews.value[index] = { ...visitedViews.value[index], ...view }
      saveToStorage()
    }
  }

  /**
   * 清理无效的标签页
   * 在路由刷新后调用，移除那些对应路由已被删除或禁用的标签
   * @param {Object} router Vue Router实例
   * @param {string} currentPath 当前路由路径
   * @returns {Object} 清理结果 { removedCount, needRedirect, redirectPath }
   */
  const cleanInvalidViews = (router, currentPath = '') => {
    const validRoutes = router.getRoutes()
    const validPaths = validRoutes.map(route => route.path)

    let currentViewRemoved = false

    // 过滤出仍然有效的标签页
    const validViews = visitedViews.value.filter(view => {
      // 保留固定标签（如仪表盘）
      if (view.meta?.affix) {
        return true
      }

      // 检查路由是否仍然存在
      const routeExists = validPaths.includes(view.path)
      if (!routeExists) {
        console.log(`移除无效标签页: ${view.title} (${view.path})`)

        // 检查被移除的是否是当前页面
        if (view.path === currentPath) {
          currentViewRemoved = true
        }

        return false
      }

      return true
    })

    // 更新标签页列表
    const removedCount = visitedViews.value.length - validViews.length
    let redirectPath = null

    if (removedCount > 0) {
      visitedViews.value = validViews

      // 同时清理缓存
      const validViewNames = validViews.map(view => view.name).filter(Boolean)
      cachedViews.value = cachedViews.value.filter(name => validViewNames.includes(name))

      // 如果当前页面被移除，确定重定向路径
      if (currentViewRemoved) {
        if (validViews.length > 0) {
          // 跳转到最后一个有效标签
          redirectPath = validViews[validViews.length - 1].path
        } else {
          // 如果没有其他标签，跳转到仪表盘
          redirectPath = '/dashboard'
        }
      }

      // 保存到存储
      saveToStorage()

      console.log(`已清理 ${removedCount} 个无效标签页`)
    }

    return {
      removedCount,
      needRedirect: currentViewRemoved,
      redirectPath
    }
  }

  /**
   * 清除所有数据
   */
  const clearAllData = () => {
    visitedViews.value = []
    cachedViews.value = []
    try {
      localStorage.removeItem(STORAGE_KEY)
    } catch (error) {
      console.warn('清除标签页数据失败:', error)
    }
  }

  return {
    // 状态
    visitedViews,
    cachedViews,

    // 方法
    initTagsView,
    addView,
    addVisitedView,
    addCachedView,
    delView,
    delVisitedView,
    delCachedView,
    delOthersViews,
    delOthersVisitedViews,
    delOthersCachedViews,
    delAllViews,
    delAllVisitedViews,
    delAllCachedViews,
    updateVisitedView,
    cleanInvalidViews,
    clearAllData
  }
})
