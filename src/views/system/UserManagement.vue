<template>
  <div class="management-page">
    <!-- 搜索和操作区域 -->
    <div class="search-section">
      <el-form :model="searchForm" inline class="search-form">
        <el-form-item label="用户账号">
          <el-input
            v-model="searchForm.username"
            placeholder="请输入用户账号"
            clearable
            style="width: 200px"
            @keyup.enter="handleSearch"
          />
        </el-form-item>
        <el-form-item label="用户类型">
          <el-select
            v-model="searchForm.userTypeId"
            placeholder="请选择用户类型"
            clearable
            style="width: 180px"
          >
            <el-option
              v-for="type in userTypes"
              :key="type.id"
              :label="type.typeName"
              :value="type.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="账户状态">
          <el-select
            v-model="searchForm.accountLocked"
            placeholder="请选择账户状态"
            clearable
            style="width: 150px"
          >
            <el-option label="正常" :value="0" />
            <el-option label="已锁定" :value="1" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="handleReset">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
        </el-form-item>
      </el-form>

      <div class="action-buttons">
        <el-button
          type="primary"
          @click="handleAdd"
        >
          <el-icon><Plus /></el-icon>
          新增用户
        </el-button>
        <el-button
          type="success"
          :disabled="!hasSelection"
          @click="handleBatchUnlock"
        >
          <el-icon><Unlock /></el-icon>
          批量解锁
        </el-button>
        <el-button
          type="warning"
          :disabled="!hasSelection"
          @click="handleBatchLock"
        >
          <el-icon><Lock /></el-icon>
          批量锁定
        </el-button>
        <el-button
          type="danger"
          :disabled="!hasSelection"
          @click="handleBatchDelete"
        >
          <el-icon><Delete /></el-icon>
          批量删除
        </el-button>
      </div>
    </div>

    <!-- 数据表格 -->
    <div class="table-section">
      <el-table
        v-loading="loading"
        :data="tableData"
        @selection-change="handleSelectionChange"
        style="width: 100%"
        :header-cell-style="{ background: 'var(--bg-color-table-header)', color: 'var(--text-color-primary)', fontWeight: '600' }"
        :row-style="{ height: '56px' }"
      >
        <el-table-column type="selection" width="55" :selectable="isSelectable" />
        <el-table-column label="头像" width="80" align="center" header-align="center">
          <template #default="{ row }">
            <UserAvatar :username="row.username" :size="32" />
          </template>
        </el-table-column>
        <el-table-column prop="username" label="用户账号" min-width="120" align="center" header-align="center">
          <template #default="{ row }">
            <div class="user-info">
              <span class="username">{{ row.username }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="userTypeId" label="用户类型" width="150" align="center" header-align="center">
          <template #default="{ row }">
            <span>{{ getUserTypeName(row.userTypeId) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="accountLocked" label="账户状态" width="100" align="center" header-align="center">
          <template #default="{ row }">
            <el-tag :type="row.accountLocked === 0 ? 'success' : 'danger'" size="small">
              {{ row.accountLocked === 0 ? '正常' : '已锁定' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="hasGoogleAuth" label="谷歌验证" width="100" align="center" header-align="center">
          <template #default="{ row }">
            <el-tag :type="row.hasGoogleAuth ? 'success' : 'info'" size="small">
              {{ row.hasGoogleAuth ? '已设置' : '未设置' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="lastLoginTime" label="最后登录时间" width="180" align="center" header-align="center">
          <template #default="{ row }">
            {{ formatDateTime(row.lastLoginTime) }}
          </template>
        </el-table-column>
        <el-table-column prop="lastLoginIp" label="最后登录IP" width="140" align="center" header-align="center">
          <template #default="{ row }">
            {{ row.lastLoginIp || '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" width="180" align="center" header-align="center">
          <template #default="{ row }">
            {{ formatDateTime(row.createTime) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="220" fixed="right" align="center" header-align="center">
          <template #default="{ row }">
            <template v-if="!row.isAdmin">
              <div class="action-buttons-row">
                <el-tooltip content="编辑" placement="top">
                  <el-button
                    type="primary"
                    size="small"
                    @click="handleEdit(row)"
                    :icon="Edit"
                    circle
                  />
                </el-tooltip>
                <el-tooltip :content="row.accountLocked === 0 ? '锁定' : '解锁'" placement="top">
                  <el-button
                    :type="row.accountLocked === 0 ? 'warning' : 'success'"
                    size="small"
                    @click="handleToggleLock(row)"
                    :icon="row.accountLocked === 0 ? Lock : Unlock"
                    circle
                  />
                </el-tooltip>
                
                <el-dropdown @command="(command) => handleDropdownCommand(command, row)" trigger="click">
                  <el-button size="small" :icon="ArrowDown" circle title="更多操作" />
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item command="resetPassword">
                        <el-icon><Key /></el-icon>
                        重置密码
                      </el-dropdown-item>
                      <el-dropdown-item command="resetGoogleAuth" v-if="row.hasGoogleAuth">
                        <el-icon><Key /></el-icon>
                        重置谷歌
                      </el-dropdown-item>
                      <el-dropdown-item command="delete" divided>
                        <el-icon><Delete /></el-icon>
                        删除用户
                      </el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </div>
            </template>
            <template v-else>
              <el-text type="info" size="small">该用户不支持被操作</el-text>
            </template>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <Pagination
        v-model:current="pagination.current"
        v-model:pageSize="pagination.size"
        :total="pagination.total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 编辑用户对话框 -->
    <UserEditDialog
      v-model:visible="editDialogVisible"
      :user-data="currentUser"
      :user-types="userTypes"
      @success="handleEditSuccess"
    />

    <!-- 新增用户对话框 -->
    <UserAddDialog
      v-model:visible="addDialogVisible"
      :user-types="userTypes"
      @success="handleAddSuccess"
    />

    <!-- 重置密码对话框 -->
    <UserResetPasswordDialog
      v-model:visible="resetPasswordDialogVisible"
      :user-data="currentUser"
      @success="handleResetPasswordSuccess"
    />
  </div>
</template>

// 定义组件名称，用于keep-alive缓存
defineOptions({
  name: 'UserManagement'
})

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Search, Refresh, Unlock, Lock, Delete, Edit, ArrowDown,
  Key, Plus
} from '@element-plus/icons-vue'
import {
  getUserPage, getUserTypes, resetUserGoogleAuth, deleteUser, batchDeleteUsers,
  lockUser, unlockUser, batchLockUsers, batchUnlockUsers
} from '@/api/user'
import { formatDateTime } from '@/utils/date'
import UserEditDialog from './components/UserEditDialog.vue'
import UserAddDialog from './components/UserAddDialog.vue'
import UserResetPasswordDialog from './components/UserResetPasswordDialog.vue'
import UserAvatar from '@/components/UserAvatar.vue'

// 响应式数据
const loading = ref(false)
const tableData = ref([])
const userTypes = ref([])
const selectedUsers = ref([])
const editDialogVisible = ref(false)
const addDialogVisible = ref(false)
const resetPasswordDialogVisible = ref(false)
const currentUser = ref({})

// 搜索表单
const searchForm = reactive({
  username: '',
  userTypeId: '',
  accountLocked: null
})

// 分页数据
const pagination = reactive({
  current: 1,
  size: 10,
  total: 0
})

// 计算属性
const hasSelection = computed(() => selectedUsers.value.length > 0)

// 判断行是否可选择（超级管理员不可选择）
const isSelectable = (row) => {
  return !row.isAdmin
}

// 获取用户类型名称
const getUserTypeName = (userTypeId) => {
  const userType = userTypes.value.find(type => type.id === userTypeId)
  return userType ? userType.typeName : '未知类型'
}

// 获取用户列表
const fetchUsers = async () => {
  try {
    loading.value = true
    const params = {
      current: pagination.current,
      size: pagination.size,
      ...searchForm
    }
    
    const response = await getUserPage(params)
    tableData.value = response.records || []
    pagination.total = response.total || 0
  } catch (error) {
    // 错误提示由响应拦截器处理，这里只需要记录日志
    console.error('获取用户列表失败:', error)
  } finally {
    loading.value = false
  }
}

// 获取用户类型列表
const fetchUserTypes = async () => {
  try {
    const response = await getUserTypes()
    userTypes.value = response || []
  } catch (error) {
    console.error('获取用户类型列表失败:', error)
  }
}

// 搜索
const handleSearch = () => {
  pagination.current = 1
  fetchUsers()
}

// 重置搜索
const handleReset = () => {
  Object.assign(searchForm, {
    username: '',
    userTypeId: '',
    accountLocked: null
  })
  pagination.current = 1
  fetchUsers()
}

// 选择变化
const handleSelectionChange = (selection) => {
  selectedUsers.value = selection
}

// 编辑用户
const handleEdit = (row) => {
  currentUser.value = { ...row }
  editDialogVisible.value = true
}

// 编辑成功回调
const handleEditSuccess = () => {
  editDialogVisible.value = false
  fetchUsers()
}

// 新增用户
const handleAdd = () => {
  addDialogVisible.value = true
}

// 新增成功回调
const handleAddSuccess = () => {
  addDialogVisible.value = false
  fetchUsers()
}

// 切换锁定状态
const handleToggleLock = async (row) => {
  const action = row.accountLocked === 0 ? '锁定' : '解锁'
  try {
    await ElMessageBox.confirm(
      `确定要${action}用户"${row.username}"吗？`,
      `${action}确认`,
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    if (row.accountLocked === 0) {
      await lockUser(row.id)
    } else {
      await unlockUser(row.id)
    }
    
    ElMessage.success(`${action}成功`)
    fetchUsers()
  } catch (error) {
    if (error !== 'cancel') {
      // 错误提示由响应拦截器处理，这里只需要记录日志
      console.error(`${action}用户失败:`, error)
    }
  }
}

// 下拉菜单命令处理
const handleDropdownCommand = async (command, row) => {
  switch (command) {
    case 'resetPassword':
      handleResetPassword(row)
      break
    case 'resetGoogleAuth':
      await handleResetGoogleAuth(row)
      break
    case 'delete':
      await handleDelete(row)
      break
  }
}

// 重置密码
const handleResetPassword = (row) => {
  currentUser.value = { ...row }
  resetPasswordDialogVisible.value = true
}

// 重置密码成功回调
const handleResetPasswordSuccess = () => {
  resetPasswordDialogVisible.value = false
  // 重置密码不需要刷新列表，因为密码不在列表中显示
}

// 重置谷歌验证
const handleResetGoogleAuth = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确定要重置用户"${row.username}"的谷歌验证KEY吗？`,
      '重置谷歌验证确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await resetUserGoogleAuth(row.id)
    ElMessage.success('谷歌验证KEY重置成功')
    fetchUsers()
  } catch (error) {
    if (error !== 'cancel') {
      // 错误提示由响应拦截器处理，这里只需要记录日志
      console.error('重置谷歌验证失败:', error)
    }
  }
}

// 删除用户
const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除用户"${row.username}"吗？此操作不可恢复！`,
      '删除确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'error'
      }
    )

    await deleteUser(row.id)
    ElMessage.success('删除成功')
    fetchUsers()
  } catch (error) {
    if (error !== 'cancel') {
      // 错误提示由响应拦截器处理，这里只需要记录日志
      console.error('删除用户失败:', error)
    }
  }
}

// 批量解锁
const handleBatchUnlock = async () => {
  try {
    await ElMessageBox.confirm(
      `确定要解锁选中的 ${selectedUsers.value.length} 个用户吗？`,
      '批量解锁确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const userIds = selectedUsers.value.map(user => user.id)
    await batchUnlockUsers(userIds)
    ElMessage.success('批量解锁成功')
    fetchUsers()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量解锁失败:', error)
      ElMessage.error('批量解锁失败')
    }
  }
}

// 批量锁定
const handleBatchLock = async () => {
  try {
    await ElMessageBox.confirm(
      `确定要锁定选中的 ${selectedUsers.value.length} 个用户吗？`,
      '批量锁定确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const userIds = selectedUsers.value.map(user => user.id)
    await batchLockUsers(userIds)
    ElMessage.success('批量锁定成功')
    fetchUsers()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量锁定失败:', error)
      ElMessage.error('批量锁定失败')
    }
  }
}

// 批量删除
const handleBatchDelete = async () => {
  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${selectedUsers.value.length} 个用户吗？此操作不可恢复！`,
      '批量删除确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'error'
      }
    )

    const userIds = selectedUsers.value.map(user => user.id)
    await batchDeleteUsers(userIds)
    ElMessage.success('批量删除成功')
    fetchUsers()
  } catch (error) {
    if (error !== 'cancel') {
      // 错误提示由响应拦截器处理，这里只需要记录日志
      console.error('批量删除失败:', error)
    }
  }
}

// 分页大小变化
const handleSizeChange = (size) => {
  pagination.size = size
  pagination.current = 1
  fetchUsers()
}

// 当前页变化
const handleCurrentChange = (current) => {
  pagination.current = current
  fetchUsers()
}

// 初始化
onMounted(() => {
  fetchUserTypes()
  fetchUsers()
})
</script>

<style scoped>
/* 使用全局统一的管理页面样式 */

/* 响应式设计 */
@media (max-width: 768px) {
  .management-page {
    padding: 16px;
  }

  .search-form :deep(.el-form) {
    flex-direction: column;
  }

  .search-form :deep(.el-form-item) {
    margin-right: 0;
    margin-bottom: 16px;
  }
}
.action-buttons-row {
  display: flex;
  gap: 12px;
  justify-content: flex-start;
  align-items: center;

  .el-button + .el-button {
    margin-left: 0px;
  }
}

/* 移动端响应式样式 */
@media (max-width: 768px) {
  .search-form {
    .el-form-item {
      width: 100%;
      margin-right: 0;
      margin-bottom: 16px;

      .el-input,
      .el-select {
        width: 100% !important;
      }
    }

    /* 搜索按钮区域 */
    .el-form-item:last-child {
      display: flex;
      gap: 8px;

      .el-button {
        flex: 1;
      }
    }
  }

  .action-buttons {
    flex-direction: column;
    gap: 12px;

    .el-button {
      width: 100%;
      margin-left: 0;
    }
  }

  /* 移动端头像适配 */
  .user-info {
    .username {
      font-size: 14px;
    }
  }
}

/* 用户信息样式 */
.user-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;

  .username {
    font-weight: 500;
    color: var(--text-color-primary);
    line-height: 1.4;
  }
}

</style>
